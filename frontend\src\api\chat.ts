// 聊天相关 API 封装
export function streamChatMessage({
    conversationId,
    message,
    collectionName = "FinancialResearchOffice",
    input = { class_tag: "123" },
    onMessage,
    onError,
    onComplete
}: {
    conversationId: number,
    message: string,
    collectionName?: string,
    input?: Record<string, any>,
    onMessage: (data: string) => void,
    onError?: (err: any) => void,
    onComplete?: () => void
}) {
    // 获取认证token
    const token = localStorage.getItem('access_token');

    const url = `/api/chat/stream`;

    // 构建请求headers
    const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
    };

    // 添加Authorization header（必填）
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    // 构建请求体
    const requestBody = {
        collection_name: collectionName,
        input: input,
        conversation_id: conversationId,
        message: message
    };

    // 使用fetch + ReadableStream替代EventSource
    const controller = new AbortController();

    fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(requestBody),
        signal: controller.signal
    })
    .then(async (response) => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        if (!response.body) {
            throw new Error('Response body is null');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) {
                    if (onComplete) onComplete();
                    break;
                }

                // 解码数据并添加到缓冲区
                buffer += decoder.decode(value, { stream: true });

                // 按行分割处理
                const lines = buffer.split('\n');
                // 保留最后一行（可能不完整）
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6); // 移除 "data: " 前缀

                        if (data === '[DONE]') {
                            if (onComplete) onComplete();
                            return;
                        }

                        if (data.trim()) {
                            try {
                                // 尝试解析JSON格式的数据
                                const parsed = JSON.parse(data);
                                if (parsed.error) {
                                    if (onError) onError(new Error(parsed.error));
                                    return;
                                } else if (parsed.content) {
                                    onMessage(parsed.content);
                                }
                            } catch (e) {
                                // 如果不是JSON格式，直接传递原始数据
                                onMessage(data);
                            }
                        }
                    } else if (line.startsWith('event: ')) {
                        const eventType = line.slice(7); // 移除 "event: " 前缀
                        if (eventType === 'end') {
                            if (onComplete) onComplete();
                            return;
                        }
                    }
                }
            }
        } catch (error) {
            if (onError) onError(error);
        } finally {
            reader.releaseLock();
        }
    })
    .catch((error) => {
        if (onError) onError(error);
    });

    // 返回一个包含abort方法的对象，用于取消请求
    return {
        close: () => controller.abort(),
        abort: () => controller.abort()
    };
}