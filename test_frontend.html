<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式输出测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .input-group {
            margin: 10px 0;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="text"] {
            width: 300px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>流式输出测试页面</h1>
    
    <div class="container">
        <h2>配置</h2>
        <div class="input-group">
            <label>API URL:</label>
            <input type="text" id="apiUrl" value="http://localhost:3000/api/chat/stream">
        </div>
        <div class="input-group">
            <label>Token:</label>
            <input type="text" id="token" placeholder="Bearer token (可选)">
        </div>
        <div class="input-group">
            <label>消息:</label>
            <input type="text" id="message" value="你好，请测试流式输出" placeholder="输入测试消息">
        </div>
        <button onclick="testStream()" id="testBtn">测试流式输出</button>
        <button onclick="clearOutput()">清空输出</button>
    </div>

    <div class="container">
        <h2>状态</h2>
        <div id="status" class="status info">准备就绪</div>
    </div>

    <div class="container">
        <h2>流式输出</h2>
        <div id="output" class="output">等待输出...</div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function appendOutput(text) {
            const outputEl = document.getElementById('output');
            outputEl.textContent += text;
            outputEl.scrollTop = outputEl.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
            updateStatus('输出已清空', 'info');
        }

        async function testStream() {
            const apiUrl = document.getElementById('apiUrl').value;
            const token = document.getElementById('token').value;
            const message = document.getElementById('message').value;
            const testBtn = document.getElementById('testBtn');

            if (!message.trim()) {
                updateStatus('请输入测试消息', 'error');
                return;
            }

            testBtn.disabled = true;
            clearOutput();
            updateStatus('开始测试流式输出...', 'info');

            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            };

            if (token.trim()) {
                headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
            }

            const requestBody = {
                conversation_id: 1,
                message: message,
                collection_name: "test",
                input: {}
            };

            try {
                updateStatus('发送请求...', 'info');
                
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                updateStatus('开始接收流式数据...', 'success');

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();

                    if (done) {
                        updateStatus('流式输出完成', 'success');
                        break;
                    }

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);

                            if (data === '[DONE]') {
                                updateStatus('流式输出完成 (收到 [DONE])', 'success');
                                testBtn.disabled = false;
                                return;
                            }

                            if (data.trim()) {
                                try {
                                    const parsed = JSON.parse(data);
                                    if (parsed.error) {
                                        updateStatus(`错误: ${parsed.error}`, 'error');
                                        appendOutput(`\n[错误] ${parsed.error}\n`);
                                    } else if (parsed.content) {
                                        appendOutput(parsed.content);
                                    }
                                } catch (e) {
                                    appendOutput(data);
                                }
                            }
                        }
                    }
                }

            } catch (error) {
                updateStatus(`请求失败: ${error.message}`, 'error');
                appendOutput(`\n[错误] ${error.message}\n`);
            } finally {
                testBtn.disabled = false;
            }
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面已加载，可以开始测试', 'info');
        });
    </script>
</body>
</html>
