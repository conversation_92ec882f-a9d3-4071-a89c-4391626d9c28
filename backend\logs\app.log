2025-07-25 15:03:20,840 INFO services.ldap_service 尝试 LDAP 认证: cn=wangzhixin,dc=users,dc=appdata,dc=erayt,dc=com
2025-07-25 15:03:20,874 INFO services.ldap_service [LDAP认证成功]: wangzhixin
2025-07-25 15:06:37,115 INFO services.ldap_service 尝试 LDAP 认证: cn=wangzhixin,dc=users,dc=appdata,dc=erayt,dc=com
2025-07-25 15:06:37,167 INFO services.ldap_service [LDAP认证成功]: wangzhixin
2025-07-25 15:24:21,195 INFO services.ldap_service 尝试 LDAP 认证: cn=wangzhixin,dc=users,dc=appdata,dc=erayt,dc=com
2025-07-25 15:24:21,234 INFO services.ldap_service [LDAP认证成功]: wangzhixin
2025-07-25 15:24:34,089 INFO services.ldap_service 尝试 LDAP 认证: cn=wangzhixin,dc=users,dc=appdata,dc=erayt,dc=com
2025-07-25 15:24:34,120 INFO services.ldap_service [LDAP认证成功]: wangzhixin
2025-07-25 15:36:20,574 INFO api.chat [chat_stream_get] 用户: wangzhixin, 对话ID: 1753428974443, 消息: 你好
2025-07-25 15:49:40,547 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753429777251, 消息: 你好
2025-07-25 15:49:40,568 WARNING api.chat [chat_stream] 参数错误: 会话不存在或无权限访问
2025-07-25 15:54:22,955 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753429777251, 消息: 你好
2025-07-25 15:54:22,987 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=1, role='user')>
2025-07-25 15:54:33,431 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 15:54:33,433 ERROR services.llm_service 生成回复失败: unhashable type: 'RAGRetriever'
2025-07-25 15:54:33,439 INFO api.chat [chat_stream] AI回复已保存, 长度: 44
2025-07-25 15:55:58,855 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753429777251, 消息: 你好
2025-07-25 15:55:58,879 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=3, role='user')>
2025-07-25 15:56:02,574 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 15:56:02,575 ERROR api.chat [chat_stream] 处理消息时发生异常: 'TypeError' object has no attribute 'exc_info'
Traceback (most recent call last):
  File "D:\code\chatbot-ui\chatbot\backend\services\llm_service.py", line 98, in generate_chat_response
    rag_chain = self._create_rag_chain(collection_name, input)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\services\llm_service.py", line 57, in _create_rag_chain
    retriever = RAGRetriever(collection_name, input)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\rag\rag_retriever.py", line 29, in __init__
    self.milvus_service = self._create_milvus_service(collection_name)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: unhashable type: 'RAGRetriever'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\chatbot-ui\chatbot\backend\api\chat.py", line 40, in generate_response
    for chunk in message_service.generate_chat_response(chat_data):
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\services\message_service.py", line 84, in generate_chat_response
    yield from llm_service.generate_chat_response(chat_data.message, chat_data.collection_name, chat_data.input)
  File "D:\code\chatbot-ui\chatbot\backend\services\llm_service.py", line 105, in generate_chat_response
    logger.error(f"生成回复失败: {e.exc_info()}")
                                  ^^^^^^^^^^
AttributeError: 'TypeError' object has no attribute 'exc_info'
2025-07-25 15:58:27,138 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753429777251, 消息: 你好
2025-07-25 15:58:27,195 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=4, role='user')>
2025-07-25 15:58:31,436 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 15:58:32,690 ERROR api.chat [chat_stream] 处理消息时发生异常: 'SchemaNotReadyException' object has no attribute 'exc_info'
Traceback (most recent call last):
  File "D:\code\chatbot-ui\chatbot\backend\services\llm_service.py", line 98, in generate_chat_response
    rag_chain = self._create_rag_chain(collection_name, input)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\services\llm_service.py", line 57, in _create_rag_chain
    retriever = RAGRetriever(collection_name, input)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\rag\rag_retriever.py", line 29, in __init__
    self.milvus_service = self._create_milvus_service(collection_name)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\rag\rag_retriever.py", line 35, in _create_milvus_service
    return get_milvus_service_by_collection(collection_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\rag\vectorstore_search.py", line 1184, in get_milvus_service_by_collection
    return VectorStore_Search(
           ^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\rag\vectorstore_search.py", line 67, in __init__
    self.collection = Collection(self.collection_name)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\.venv\Lib\site-packages\pymilvus\orm\collection.py", line 141, in __init__
    raise SchemaNotReadyException(
pymilvus.exceptions.SchemaNotReadyException: <SchemaNotReadyException: (code=1, message=Collection 'my_collection' not exist, or you can pass in schema to create one.)>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\chatbot-ui\chatbot\backend\api\chat.py", line 40, in generate_response
    for chunk in message_service.generate_chat_response(chat_data):
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\services\message_service.py", line 84, in generate_chat_response
    yield from llm_service.generate_chat_response(chat_data.message, chat_data.collection_name, chat_data.input)
  File "D:\code\chatbot-ui\chatbot\backend\services\llm_service.py", line 105, in generate_chat_response
    logger.error(f"生成回复失败: {e.exc_info()}")
                                  ^^^^^^^^^^
AttributeError: 'SchemaNotReadyException' object has no attribute 'exc_info'
2025-07-25 15:59:36,371 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753429777251, 消息: 你好
2025-07-25 15:59:36,382 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=5, role='user')>
2025-07-25 15:59:36,382 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 15:59:37,595 ERROR api.chat [chat_stream] 处理消息时发生异常: 'SchemaNotReadyException' object has no attribute 'exc_info'
Traceback (most recent call last):
  File "D:\code\chatbot-ui\chatbot\backend\services\llm_service.py", line 98, in generate_chat_response
    rag_chain = self._create_rag_chain(collection_name, input)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\services\llm_service.py", line 57, in _create_rag_chain
    retriever = RAGRetriever(collection_name, input)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\rag\rag_retriever.py", line 29, in __init__
    self.milvus_service = self._create_milvus_service(collection_name)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\rag\rag_retriever.py", line 35, in _create_milvus_service
    return get_milvus_service_by_collection(collection_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\rag\vectorstore_search.py", line 1184, in get_milvus_service_by_collection
    return VectorStore_Search(
           ^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\rag\vectorstore_search.py", line 67, in __init__
    self.collection = Collection(self.collection_name)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\.venv\Lib\site-packages\pymilvus\orm\collection.py", line 141, in __init__
    raise SchemaNotReadyException(
pymilvus.exceptions.SchemaNotReadyException: <SchemaNotReadyException: (code=1, message=Collection 'my_collection' not exist, or you can pass in schema to create one.)>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\code\chatbot-ui\chatbot\backend\api\chat.py", line 40, in generate_response
    for chunk in message_service.generate_chat_response(chat_data):
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\code\chatbot-ui\chatbot\backend\services\message_service.py", line 84, in generate_chat_response
    yield from llm_service.generate_chat_response(chat_data.message, chat_data.collection_name, chat_data.input)
  File "D:\code\chatbot-ui\chatbot\backend\services\llm_service.py", line 105, in generate_chat_response
    logger.error(f"生成回复失败: {e.exc_info()}")
                                  ^^^^^^^^^^
AttributeError: 'SchemaNotReadyException' object has no attribute 'exc_info'
2025-07-25 16:00:23,342 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753430418848, 消息: 你好
2025-07-25 16:00:23,383 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=6, role='user')>
2025-07-25 16:00:26,227 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 16:00:27,364 ERROR services.llm_service 生成回复失败: <SchemaNotReadyException: (code=1, message=Collection 'my_collection' not exist, or you can pass in schema to create one.)>
2025-07-25 16:00:27,377 INFO api.chat [chat_stream] AI回复已保存, 长度: 136
2025-07-25 16:01:02,395 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753430457693, 消息: 你好
2025-07-25 16:01:02,420 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=8, role='user')>
2025-07-25 16:01:02,420 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 16:01:03,576 ERROR services.llm_service 生成回复失败: <SchemaNotReadyException: (code=1, message=Collection 'my_collection' not exist, or you can pass in schema to create one.)>
2025-07-25 16:01:03,587 INFO api.chat [chat_stream] AI回复已保存, 长度: 136
2025-07-25 16:02:23,199 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753430538896, 消息: 你好
2025-07-25 16:02:23,218 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=10, role='user')>
2025-07-25 16:02:23,219 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 16:02:24,455 ERROR services.llm_service 生成回复失败: <SchemaNotReadyException: (code=1, message=Collection 'my_collection' not exist, or you can pass in schema to create one.)>
2025-07-25 16:02:24,465 INFO api.chat [chat_stream] AI回复已保存, 长度: 136
2025-07-25 16:03:42,545 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753430620058, 消息: 你好
2025-07-25 16:03:42,567 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=12, role='user')>
2025-07-25 16:03:42,567 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 16:03:43,802 ERROR services.llm_service 生成回复失败: "RAGRetriever" object has no field "milvus_service"
2025-07-25 16:03:43,812 INFO api.chat [chat_stream] AI回复已保存, 长度: 64
2025-07-25 16:05:09,841 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753430620058, 消息: 你好
2025-07-25 16:05:09,861 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=14, role='user')>
2025-07-25 16:05:13,302 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 16:05:14,427 ERROR services.llm_service 生成回复失败: None
2025-07-25 16:05:14,439 INFO api.chat [chat_stream] AI回复已保存, 长度: 64
2025-07-25 16:10:36,450 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753430620058, 消息: 你好
2025-07-25 16:10:36,477 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=16, role='user')>
2025-07-25 16:10:39,807 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 16:10:39,829 ERROR services.llm_service 生成回复失败: None
2025-07-25 16:10:39,839 INFO api.chat [chat_stream] AI回复已保存, 长度: 358
2025-07-25 16:12:14,392 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753430620058, 消息: 你好
2025-07-25 16:12:14,418 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=18, role='user')>
2025-07-25 16:12:17,696 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 16:12:18,841 ERROR services.llm_service 生成回复失败: None
2025-07-25 16:12:18,851 INFO api.chat [chat_stream] AI回复已保存, 长度: 64
2025-07-25 16:15:02,985 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753430620058, 消息: 你好
2025-07-25 16:15:03,025 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=20, role='user')>
2025-07-25 16:15:06,663 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 16:15:07,865 INFO rag.rag_retriever 开始检索文档: 你好...
2025-07-25 16:15:07,866 ERROR rag.rag_retriever 检索文档失败: 'k'
2025-07-25 16:15:08,099 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-25 16:15:08,498 INFO api.chat [chat_stream] AI回复已保存, 长度: 16
2025-07-25 16:15:56,703 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753430620058, 消息: 你好
2025-07-25 16:15:56,731 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=22, role='user')>
2025-07-25 16:15:59,936 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 16:16:01,227 INFO rag.rag_retriever 开始检索文档: 你好...
2025-07-25 16:16:01,229 ERROR rag.rag_retriever 检索文档失败: None
2025-07-25 16:16:01,379 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-25 16:16:01,755 INFO api.chat [chat_stream] AI回复已保存, 长度: 16
2025-07-25 16:18:01,494 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753431453178, 消息: 固定收益产品的定义是什么？
2025-07-25 16:18:01,528 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=24, role='user')>
2025-07-25 16:18:01,528 INFO services.llm_service 开始生成回复: 固定收益产品的定义是什么？...
2025-07-25 16:18:02,764 INFO rag.rag_retriever 开始检索文档: 固定收益产品的定义是什么？...
2025-07-25 16:18:02,767 ERROR rag.rag_retriever 检索文档失败: None
2025-07-25 16:18:02,904 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-25 16:18:10,865 INFO api.chat [chat_stream] AI回复已保存, 长度: 194
2025-07-25 16:22:02,694 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753431453178, 消息: 你好
2025-07-25 16:22:02,718 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=26, role='user')>
2025-07-25 16:22:05,913 INFO services.llm_service 开始生成回复: 你好...
2025-07-25 16:22:07,502 INFO rag.rag_retriever 开始检索文档: 你好...
2025-07-25 16:22:07,505 ERROR rag.rag_retriever 检索文档失败: None
2025-07-25 16:22:07,668 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-25 16:22:07,986 INFO api.chat [chat_stream] AI回复已保存, 长度: 16
2025-07-25 16:22:45,340 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753431453178, 消息: 年后
2025-07-25 16:22:45,368 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=28, role='user')>
2025-07-25 16:22:48,682 INFO services.llm_service 开始生成回复: 年后...
2025-07-25 16:22:49,986 INFO rag.rag_retriever 开始检索文档: 年后...
2025-07-25 16:22:49,988 ERROR rag.rag_retriever 检索文档失败: None
2025-07-25 16:22:50,124 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-25 16:22:52,790 INFO api.chat [chat_stream] AI回复已保存, 长度: 153
2025-07-25 16:23:46,337 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753431453178, 消息: 固定收益产品的定义是什么？
2025-07-25 16:23:46,369 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=30, role='user')>
2025-07-25 16:23:50,100 INFO services.llm_service 开始生成回复: 固定收益产品的定义是什么？...
2025-07-25 16:23:51,599 INFO rag.rag_retriever 开始检索文档: 固定收益产品的定义是什么？...
2025-07-25 16:23:51,722 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/embeddings "HTTP/1.1 200 OK"
2025-07-25 16:23:51,749 ERROR rag.rag_retriever 检索文档失败: None
2025-07-25 16:23:51,838 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-25 16:23:54,632 INFO api.chat [chat_stream] AI回复已保存, 长度: 162
2025-07-25 16:25:37,343 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753431453178, 消息: 固定收益产品的定义是什么？
2025-07-25 16:25:37,390 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=32, role='user')>
2025-07-25 16:25:41,336 INFO services.llm_service 开始生成回复: 固定收益产品的定义是什么？...
2025-07-25 16:25:42,768 INFO rag.rag_retriever 开始检索文档: 固定收益产品的定义是什么？...
2025-07-25 16:25:42,881 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/embeddings "HTTP/1.1 200 OK"
2025-07-25 16:25:42,917 INFO rag.rag_retriever 检索到 5 个相关文档
2025-07-25 16:25:42,919 ERROR rag.rag_retriever 检索文档失败: None
2025-07-25 16:25:43,031 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-25 16:25:45,434 INFO api.chat [chat_stream] AI回复已保存, 长度: 136
2025-07-25 16:28:38,248 INFO api.chat [chat_stream] 用户: wangzhixin, 对话ID: 1753432115414, 消息: 固定收益产品的定义是什么？
2025-07-25 16:28:38,284 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=34, role='user')>
2025-07-25 16:28:41,331 INFO services.llm_service 开始生成回复: 固定收益产品的定义是什么？...
2025-07-25 16:28:42,640 INFO rag.rag_retriever 开始检索文档: 固定收益产品的定义是什么？...
2025-07-25 16:28:42,759 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/embeddings "HTTP/1.1 200 OK"
2025-07-25 16:28:42,788 INFO rag.rag_retriever 检索到 5 个相关文档
2025-07-25 16:28:42,788 WARNING rag.rag_retriever 重排序失败，使用原始结果: 'rerank_top_k'
2025-07-25 16:28:48,637 INFO httpx HTTP Request: POST http://192.168.10.236:23000/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-25 16:28:52,488 INFO api.chat [chat_stream] AI回复已保存, 长度: 195
2025-07-25 16:40:19,394 INFO api.chat [chat_stream] 用户: testuser, 对话ID: 1, 消息: 你好，请测试流式输出
2025-07-25 16:40:19,423 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=36, role='user')>
2025-07-25 16:40:22,482 INFO services.llm_service 开始生成回复: 你好，请测试流式输出...
2025-07-25 16:40:23,687 ERROR services.llm_service 生成回复失败: None
2025-07-25 16:40:23,700 INFO api.chat [chat_stream] AI回复已保存, 长度: 127
2025-07-25 16:41:32,792 INFO api.chat [chat_stream] 用户: testuser, 对话ID: 1, 消息: 你好，请测试流式输出
2025-07-25 16:41:32,804 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=38, role='user')>
2025-07-25 16:41:32,804 INFO services.llm_service 开始生成回复: 你好，请测试流式输出...
2025-07-25 16:41:34,020 ERROR services.llm_service 生成回复失败: None
2025-07-25 16:41:34,035 INFO api.chat [chat_stream] AI回复已保存, 长度: 127
2025-07-25 16:42:09,324 INFO api.chat [chat_stream] 用户: testuser, 对话ID: 1, 消息: 你好，请测试流式输出
2025-07-25 16:42:09,333 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=40, role='user')>
2025-07-25 16:42:09,333 INFO services.llm_service 开始生成回复: 你好，请测试流式输出...
2025-07-25 16:42:10,521 ERROR services.llm_service 生成回复失败: None
2025-07-25 16:42:10,533 INFO api.chat [chat_stream] AI回复已保存, 长度: 127
2025-07-25 16:42:52,135 INFO api.chat [chat_stream] 用户: testuser, 对话ID: 1, 消息: 你好，请测试流式输出
2025-07-25 16:42:52,147 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=42, role='user')>
2025-07-25 16:42:52,147 INFO services.llm_service 开始生成回复: 你好，请测试流式输出...
2025-07-25 16:42:53,499 ERROR services.llm_service 生成回复失败: None
2025-07-25 16:42:53,512 INFO api.chat [chat_stream] AI回复已保存, 长度: 127
2025-07-25 16:43:33,412 INFO api.chat [chat_stream] 用户: testuser, 对话ID: 1, 消息: 你好，请测试流式输出
2025-07-25 16:43:33,423 INFO api.chat [chat_stream] 用户消息已保存: <Message(id=44, role='user')>
2025-07-25 16:43:33,423 INFO services.llm_service 开始生成回复: 你好，请测试流式输出...
2025-07-25 16:43:34,629 ERROR services.llm_service 生成回复失败: None
2025-07-25 16:43:34,640 INFO api.chat [chat_stream] AI回复已保存, 长度: 127
