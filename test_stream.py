#!/usr/bin/env python3
"""测试流式接口的脚本"""

import requests
import json
import sys
from datetime import datetime, timedelta
from jose import jwt

# 配置
API_BASE = "http://127.0.0.1:8000"
SECRET_KEY = "your-secret-key-change-in-production"
ALGORITHM = "HS256"

def create_test_token(username="testuser"):
    """创建测试用的 JWT token"""
    payload = {
        "sub": username,
        "exp": datetime.utcnow() + timedelta(hours=1),
        "iat": datetime.utcnow()
    }
    return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

def test_stream_api():
    """测试流式 API"""
    token = create_test_token()
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    data = {
        "conversation_id": 1,
        "message": "你好，请测试流式输出",
        "collection_name": "test",
        "input": {}
    }
    
    print("发送请求到流式接口...")
    print(f"URL: {API_BASE}/api/chat/stream")
    print(f"Data: {json.dumps(data, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            f"{API_BASE}/api/chat/stream",
            headers=headers,
            json=data,
            stream=True
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code != 200:
            print(f"错误响应: {response.text}")
            return
        
        print("\n开始接收流式数据:")
        print("-" * 50)
        
        for line in response.iter_lines(decode_unicode=True):
            if line:
                print(f"收到: {line}")
                
                if line.startswith('data: '):
                    data_part = line[6:]  # 移除 "data: " 前缀
                    
                    if data_part == '[DONE]':
                        print("流式输出完成")
                        break
                    
                    try:
                        parsed = json.loads(data_part)
                        if 'content' in parsed:
                            print(f"内容: {parsed['content']}", end='', flush=True)
                        elif 'error' in parsed:
                            print(f"错误: {parsed['error']}")
                    except json.JSONDecodeError:
                        print(f"原始数据: {data_part}", end='', flush=True)
        
        print("\n" + "-" * 50)
        print("测试完成")
        
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_stream_api()
