"""LLM服务实现，集成RAG问答链路"""

import logging
import traceback
from functools import lru_cache
from typing import List, Generator, Dict
from langchain_core.documents import Document
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnablePassthrough
from langchain_openai import ChatOpenAI

from config.settings import settings
from rag.rag_retriever import RAGRetriever

# 配置日志
logger = logging.getLogger(__name__)


class LLMService:
    """LLM服务，负责RAG问答链路"""

    def __init__(self):
        """初始化LLM服务"""
        self.llm = self._create_llm()

    def _create_llm(self) -> ChatOpenAI:
        """创建LLM实例"""
        return ChatOpenAI(
            model=settings.MODEL_CHAT,
            api_key=settings.API_KEY,
            base_url=settings.API_URL,
            temperature=0.7,
            streaming=True,
            max_tokens=2000,
        )

    def _create_rag_chain(self, collection_name: str, input):
        """创建RAG处理链"""
        # 构建系统提示模板
        system_prompt = """你是一个专业的智能助手，请根据以下参考资料回答用户的问题。

参考资料：
{context}

请根据上述参考资料回答用户问题。回答时请注意：
1. 优先使用参考资料中的信息
2. 如果参考资料不足以回答问题，请明确说明
3. 保持回答的准确性和完整性
4. 适当引用资料来源
5. 使用简洁明了的语言

用户问题：{question}

回答："""

        prompt_template = ChatPromptTemplate.from_template(system_prompt)
        retriever = RAGRetriever(collection_name, input)
        # 构建RAG链
        rag_chain = (
                {
                    "context": retriever | self._format_context,
                    "question": RunnablePassthrough(),
                }
                | prompt_template
                | self.llm
                | StrOutputParser()
        )

        return rag_chain

    def _format_context(self, documents: List[Document]) -> str:
        """格式化上下文"""
        if not documents:
            return "暂无相关参考资料。"

        context_parts = []
        for i, doc in enumerate(documents, 1):
            context_parts.append(f"## 参考资料 {i}")
            context_parts.append(doc.page_content)
            context_parts.append("")  # 空行分隔

        return "\n".join(context_parts)

    def generate_chat_response(self, message: str, collection_name: str, input: Dict = None) -> Generator[
        str, None, None]:
        """
        生成流式聊天回复

        Args:
            message: 用户消息

        Yields:
            str: 流式回复内容片段
        """
        try:
            logger.info(f"开始生成回复: {message[:50]}...")
            # 执行RAG链并流式返回
            rag_chain = self._create_rag_chain(collection_name, input)

            for chunk in rag_chain.stream(message):
                if chunk:
                    yield chunk

        except Exception as e:
            logger.error(f"生成回复失败: {traceback.print_exc() }")
            error_message = f"抱歉，生成回复时遇到错误：{str(e)}"
            for char in error_message:
                yield char


# 创建全局LLM服务实例
@lru_cache(maxsize=1)
def get_llm_service() -> LLMService:
    """获取LLM服务实例（单例模式）"""
    return LLMService()
